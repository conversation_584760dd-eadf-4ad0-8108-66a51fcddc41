generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}


model Province {
  id        Int        @id @default(autoincrement())
  name      String
  districts District[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  UserLocationAccess UserLocationAccess[]
}

model District {
  id         Int      @id @default(autoincrement())
  name       String
  provinceId Int
  province   Province @relation(fields: [provinceId], references: [id])
  sectors    Sector[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  UserLocationAccess UserLocationAccess[]
}

model Sector {
  id         Int      @id @default(autoincrement())
  name       String
  districtId Int
  district   District @relation(fields: [districtId], references: [id])
  cells      Cell[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  UserLocationAccess UserLocationAccess[]
}

model Cell {
  id       Int       @id @default(autoincrement())
  name     String
  sectorId Int
  sector   Sector    @relation(fields: [sectorId], references: [id])
  villages Village[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  UserLocationAccess UserLocationAccess[]
}

model Village {
  id     Int    @id @default(autoincrement())
  name   String
  cellId Int
  cell   Cell   @relation(fields: [cellId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  UserLocationAccess UserLocationAccess[]
}

model User {
    id              String @id @default(cuid())
    firstName       String
    lastName        String
    email           String @unique
    telephoneNumber String @unique
    roleId          String
    role            Role   @relation(fields: [roleId], references: [id])

    locations UserLocationAccess[]
    account   Account?

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model Account {
    userId String @id
    user   User   @relation(fields: [userId], references: [id])

    accountVerified   Boolean   @default(false)
    accountVerifiedAt DateTime?

    password         String?
    refreshToken     String?
    resetToken       String?
    resetTokenExpiry DateTime?

    is2FAEnabled Boolean   @default(false)
    twoFASecret  String?
    otpTempToken String?
    otpExpiresAt DateTime?

    recoveryCodes RecoveryCode[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model RecoveryCode {
    id        String  @id @default(cuid())
    accountId String
    account   Account @relation(fields: [accountId], references: [userId])

    code String
    used Boolean @default(false)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([accountId])
}

model Role {
    id         String      @id @default(cuid())
    name       String      @unique
    privileges Privilege[]

    users User[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model UserLocationAccess {
    id     String @id @default(cuid())
    userId String
    user   User   @relation(fields: [userId], references: [id])

    provinceId Int?
    province   Province? @relation(fields: [provinceId], references: [id])

    districtId Int?
    district   District? @relation(fields: [districtId], references: [id])

    sectorId Int?
    sector   Sector? @relation(fields: [sectorId], references: [id])

    cellId Int?
    cell   Cell? @relation(fields: [cellId], references: [id])

    villageId Int?
    village   Village? @relation(fields: [villageId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([userId])
    @@index([districtId])
    @@index([sectorId])
    @@index([villageId])
}

enum Privilege {
    USER_MANAGEMENT
    DATA_COLLECTION
    //system modules
}
