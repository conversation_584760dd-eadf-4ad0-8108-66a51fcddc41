import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsString,
  IsOptional,
  IsArray,
  IsInt,
  MinLength,
} from 'class-validator';

export class LocationAccessDto {
  @ApiProperty({
    description: 'Province ID',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsInt()
  provinceId?: number;

  @ApiProperty({
    description: 'District ID',
    example: 101,
    required: false,
  })
  @IsOptional()
  @IsInt()
  districtId?: number;

  @ApiProperty({
    description: 'Sector ID',
    example: 10101,
    required: false,
  })
  @IsOptional()
  @IsInt()
  sectorId?: number;

  @ApiProperty({
    description: 'Cell ID',
    example: 1010101,
    required: false,
  })
  @IsOptional()
  @IsInt()
  cellId?: number;

  @ApiProperty({
    description: 'Village ID',
    example: 101010101,
    required: false,
  })
  @IsOptional()
  @IsInt()
  villageId?: number;
}

export class CreateUserDto {
  @ApiProperty({
    description: 'User first name',
    example: '<PERSON>',
  })
  @IsString()
  @MinLength(2)
  firstName: string;

  @ApiProperty({
    description: 'User last name',
    example: 'Doe',
  })
  @IsString()
  @MinLength(2)
  lastName: string;

  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'User telephone number',
    example: '+250788123456',
  })
  @IsString()
  telephoneNumber: string;

  @ApiProperty({
    description: 'Role ID to assign to the user',
    example: 'role_123',
  })
  @IsString()
  roleId: string;

  @ApiProperty({
    description: 'Location access configuration',
    required: false,
    type: [LocationAccessDto],
  })
  @IsOptional()
  @IsArray()
  locations?: LocationAccessDto[];
}

export class CreateUserResponseDto {
  @ApiProperty({
    description: 'Created user information',
  })
  user: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    telephoneNumber: string;
    role: {
      name: string;
      privileges: string[];
    };
    createdAt: Date;
  };

  @ApiProperty({
    description: 'Success message',
    example: 'User created successfully. Setup email sent.',
  })
  message: string;
}
