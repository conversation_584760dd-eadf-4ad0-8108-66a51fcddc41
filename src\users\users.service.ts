import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { EmailService } from '../email/email.service';
import { v4 as uuidv4 } from 'uuid';
import {
  CreateUserDto,
  CreateUserResponseDto,
} from './dto/create-user.dto';
import {
  UpdateUserDto,
  UpdateUserResponseDto,
} from './dto/update-user.dto';
import {
  UserResponseDto,
  UsersListResponseDto,
} from './dto/user-response.dto';

@Injectable()
export class UsersService {
  constructor(
    private prisma: PrismaService,
    private emailService: EmailService,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<CreateUserResponseDto> {
    const { firstName, lastName, email, telephoneNumber, roleId, locations } = createUserDto;

    // Check if user already exists
    const existingUser = await this.prisma.user.findFirst({
      where: {
        OR: [
          { email },
          { telephoneNumber },
        ],
      },
    });

    if (existingUser) {
      if (existingUser.email === email) {
        throw new ConflictException('User with this email already exists');
      }
      if (existingUser.telephoneNumber === telephoneNumber) {
        throw new ConflictException('User with this telephone number already exists');
      }
    }

    // Verify role exists
    const role = await this.prisma.role.findUnique({
      where: { id: roleId },
      include: { privileges: true },
    });

    if (!role) {
      throw new NotFoundException('Role not found');
    }

    // Create user with account and locations in transaction
    const result = await this.prisma.$transaction(async (tx) => {
      // Create user
      const user = await tx.user.create({
        data: {
          firstName,
          lastName,
          email,
          telephoneNumber,
          roleId,
        },
      });

      // Create account with verification token
      const verificationToken = uuidv4();
      const tokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      await tx.account.create({
        data: {
          userId: user.id,
          resetToken: verificationToken,
          resetTokenExpiry: tokenExpiry,
        },
      });

      // Create location access if provided
      if (locations && locations.length > 0) {
        await tx.userLocationAccess.createMany({
          data: locations.map((location) => ({
            userId: user.id,
            ...location,
          })),
        });
      }

      return user;
    });

    // Send user creation email
    await this.emailService.sendUserCreationEmail(
      email,
      firstName,
      verificationToken,
    );

    // Fetch created user with role information
    const createdUser = await this.prisma.user.findUnique({
      where: { id: result.id },
      include: {
        role: {
          include: {
            privileges: true,
          },
        },
      },
    });

    return {
      user: {
        id: createdUser.id,
        firstName: createdUser.firstName,
        lastName: createdUser.lastName,
        email: createdUser.email,
        telephoneNumber: createdUser.telephoneNumber,
        role: {
          name: createdUser.role.name,
          privileges: createdUser.role.privileges,
        },
        createdAt: createdUser.createdAt,
      },
      message: 'User created successfully. Setup email sent.',
    };
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    search?: string,
    roleId?: string,
  ): Promise<UsersListResponseDto> {
    const skip = (page - 1) * limit;

    const where = {
      AND: [
        search
          ? {
              OR: [
                { firstName: { contains: search, mode: 'insensitive' as const } },
                { lastName: { contains: search, mode: 'insensitive' as const } },
                { email: { contains: search, mode: 'insensitive' as const } },
              ],
            }
          : {},
        roleId ? { roleId } : {},
      ],
    };

    const [users, total] = await Promise.all([
      this.prisma.user.findMany({
        where,
        skip,
        take: limit,
        include: {
          role: {
            include: {
              privileges: true,
            },
          },
          account: {
            select: {
              accountVerified: true,
              is2FAEnabled: true,
            },
          },
          locations: {
            include: {
              province: true,
              district: true,
              sector: true,
              cell: true,
              village: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.user.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      users: users.map((user) => ({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        telephoneNumber: user.telephoneNumber,
        role: {
          id: user.role.id,
          name: user.role.name,
          privileges: user.role.privileges,
        },
        accountVerified: user.account?.accountVerified || false,
        is2FAEnabled: user.account?.is2FAEnabled || false,
        locations: user.locations.map((location) => ({
          id: location.id,
          province: location.province,
          district: location.district,
          sector: location.sector,
          cell: location.cell,
          village: location.village,
        })),
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      })),
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findOne(id: string): Promise<UserResponseDto> {
    const user = await this.prisma.user.findUnique({
      where: { id },
      include: {
        role: {
          include: {
            privileges: true,
          },
        },
        account: {
          select: {
            accountVerified: true,
            is2FAEnabled: true,
          },
        },
        locations: {
          include: {
            province: true,
            district: true,
            sector: true,
            cell: true,
            village: true,
          },
        },
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      telephoneNumber: user.telephoneNumber,
      role: {
        id: user.role.id,
        name: user.role.name,
        privileges: user.role.privileges,
      },
      accountVerified: user.account?.accountVerified || false,
      is2FAEnabled: user.account?.is2FAEnabled || false,
      locations: user.locations.map((location) => ({
        id: location.id,
        province: location.province,
        district: location.district,
        sector: location.sector,
        cell: location.cell,
        village: location.village,
      })),
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<UpdateUserResponseDto> {
    const { firstName, lastName, email, telephoneNumber, roleId, locations } = updateUserDto;

    // Check if user exists
    const existingUser = await this.prisma.user.findUnique({
      where: { id },
    });

    if (!existingUser) {
      throw new NotFoundException('User not found');
    }

    // Check for conflicts with other users
    if (email || telephoneNumber) {
      const conflictUser = await this.prisma.user.findFirst({
        where: {
          AND: [
            { id: { not: id } },
            {
              OR: [
                email ? { email } : {},
                telephoneNumber ? { telephoneNumber } : {},
              ].filter(Boolean),
            },
          ],
        },
      });

      if (conflictUser) {
        if (conflictUser.email === email) {
          throw new ConflictException('User with this email already exists');
        }
        if (conflictUser.telephoneNumber === telephoneNumber) {
          throw new ConflictException('User with this telephone number already exists');
        }
      }
    }

    // Verify role exists if provided
    if (roleId) {
      const role = await this.prisma.role.findUnique({
        where: { id: roleId },
      });

      if (!role) {
        throw new NotFoundException('Role not found');
      }
    }

    // Update user and locations in transaction
    const updatedUser = await this.prisma.$transaction(async (tx) => {
      // Update user
      const user = await tx.user.update({
        where: { id },
        data: {
          ...(firstName && { firstName }),
          ...(lastName && { lastName }),
          ...(email && { email }),
          ...(telephoneNumber && { telephoneNumber }),
          ...(roleId && { roleId }),
        },
      });

      // Update locations if provided
      if (locations !== undefined) {
        // Delete existing locations
        await tx.userLocationAccess.deleteMany({
          where: { userId: id },
        });

        // Create new locations
        if (locations.length > 0) {
          await tx.userLocationAccess.createMany({
            data: locations.map((location) => ({
              userId: id,
              ...location,
            })),
          });
        }
      }

      return user;
    });

    // Fetch updated user with role information
    const userWithRole = await this.prisma.user.findUnique({
      where: { id },
      include: {
        role: {
          include: {
            privileges: true,
          },
        },
      },
    });

    return {
      user: {
        id: userWithRole.id,
        firstName: userWithRole.firstName,
        lastName: userWithRole.lastName,
        email: userWithRole.email,
        telephoneNumber: userWithRole.telephoneNumber,
        role: {
          name: userWithRole.role.name,
          privileges: userWithRole.role.privileges,
        },
        updatedAt: userWithRole.updatedAt,
      },
      message: 'User updated successfully',
    };
  }

  async remove(id: string): Promise<{ message: string }> {
    // Check if user exists
    const existingUser = await this.prisma.user.findUnique({
      where: { id },
    });

    if (!existingUser) {
      throw new NotFoundException('User not found');
    }

    // Delete user and related data in transaction
    await this.prisma.$transaction(async (tx) => {
      // Delete recovery codes
      await tx.recoveryCode.deleteMany({
        where: { accountId: id },
      });

      // Delete account
      await tx.account.deleteMany({
        where: { userId: id },
      });

      // Delete location access
      await tx.userLocationAccess.deleteMany({
        where: { userId: id },
      });

      // Delete user
      await tx.user.delete({
        where: { id },
      });
    });

    return { message: 'User deleted successfully' };
  }

  async resendVerificationEmail(id: string): Promise<{ message: string }> {
    const user = await this.prisma.user.findUnique({
      where: { id },
      include: { account: true },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.account) {
      throw new BadRequestException('User account not found');
    }

    if (user.account.accountVerified) {
      throw new BadRequestException('Account is already verified');
    }

    // Generate new verification token
    const verificationToken = uuidv4();
    const tokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    await this.prisma.account.update({
      where: { userId: id },
      data: {
        resetToken: verificationToken,
        resetTokenExpiry: tokenExpiry,
      },
    });

    // Send verification email
    await this.emailService.sendUserCreationEmail(
      user.email,
      user.firstName,
      verificationToken,
    );

    return { message: 'Verification email sent successfully' };
  }
}
