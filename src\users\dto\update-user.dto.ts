import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateUserDto, LocationAccessDto } from './create-user.dto';
import { IsOptional, IsArray } from 'class-validator';

export class UpdateUserDto extends PartialType(CreateUserDto) {
  @ApiProperty({
    description: 'Location access configuration',
    required: false,
    type: [LocationAccessDto],
  })
  @IsOptional()
  @IsArray()
  locations?: LocationAccessDto[];
}

export class UpdateUserResponseDto {
  @ApiProperty({
    description: 'Updated user information',
  })
  user: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    telephoneNumber: string;
    role: {
      name: string;
      privileges: string[];
    };
    updatedAt: Date;
  };

  @ApiProperty({
    description: 'Success message',
    example: 'User updated successfully',
  })
  message: string;
}
