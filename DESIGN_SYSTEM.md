# 🎨 WASH MIS Design System

A comprehensive design system for the Water, Sanitation, and Hygiene Management Information System, built for the Ministry of Infrastructure, Republic of Rwanda.

## 🌟 Overview

The WASH MIS Design System provides a cohesive set of design tokens, components, and guidelines to ensure consistency across all user interfaces. It emphasizes accessibility, modern aesthetics, and government-appropriate branding.

## 🎯 Design Principles

### 1. **Accessibility First**
- WCAG 2.1 AA compliance
- Keyboard navigation support
- Screen reader compatibility
- High contrast ratios

### 2. **Government Professional**
- Clean, trustworthy appearance
- Infrastructure-focused blue palette
- Clear hierarchy and typography
- Minimal, purposeful design

### 3. **Responsive & Mobile-First**
- Mobile-first approach
- Flexible grid system
- Touch-friendly interactions
- Progressive enhancement

### 4. **Performance Optimized**
- Lightweight CSS framework
- Efficient animations
- Optimized for fast loading
- Minimal dependencies

## 🎨 Color System

### Primary Colors (Infrastructure Blue)
```css
--primary-50: #eff6ff;   /* Very light blue */
--primary-100: #dbeafe;  /* Light blue */
--primary-200: #bfdbfe;  /* Lighter blue */
--primary-300: #93c5fd;  /* Light blue */
--primary-400: #60a5fa;  /* Medium light blue */
--primary-500: #3b82f6;  /* Base blue */
--primary-600: #2563eb;  /* Medium blue */
--primary-700: #1d4ed8;  /* Dark blue */
--primary-800: #1e40af;  /* Darker blue */
--primary-900: #1e3a8a;  /* Very dark blue */
--primary-950: #172554;  /* Darkest blue */
```

### Neutral Colors
```css
--gray-50: #f8fafc;    /* Background */
--gray-100: #f1f5f9;   /* Light background */
--gray-200: #e2e8f0;   /* Borders */
--gray-300: #cbd5e1;   /* Light borders */
--gray-400: #94a3b8;   /* Disabled text */
--gray-500: #64748b;   /* Secondary text */
--gray-600: #475569;   /* Primary text */
--gray-700: #334155;   /* Dark text */
--gray-800: #1e293b;   /* Darker text */
--gray-900: #0f172a;   /* Darkest text */
```

### Semantic Colors
```css
/* Success */
--success-50: #f0fdf4;
--success-500: #22c55e;
--success-600: #16a34a;

/* Warning */
--warning-50: #fefce8;
--warning-500: #eab308;
--warning-600: #ca8a04;

/* Error */
--error-50: #fef2f2;
--error-500: #ef4444;
--error-600: #dc2626;
```

## 📝 Typography

### Font Family
- **Primary**: Inter (modern, readable)
- **Monospace**: SF Mono, Monaco, Inconsolata, Roboto Mono

### Font Scale
```css
--text-xs: 0.75rem;    /* 12px */
--text-sm: 0.875rem;   /* 14px */
--text-base: 1rem;     /* 16px */
--text-lg: 1.125rem;   /* 18px */
--text-xl: 1.25rem;    /* 20px */
--text-2xl: 1.5rem;    /* 24px */
--text-3xl: 1.875rem;  /* 30px */
--text-4xl: 2.25rem;   /* 36px */
```

### Font Weights
```css
--font-light: 300;
--font-normal: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;
--font-extrabold: 800;
```

### Usage Examples
```html
<h1 class="text-4xl font-bold">Main Heading</h1>
<h2 class="text-2xl font-semibold">Section Heading</h2>
<p class="text-base font-normal">Body text</p>
<span class="text-sm text-gray-500">Secondary text</span>
```

## 📏 Spacing System

### Spacing Scale
```css
--space-1: 0.25rem;    /* 4px */
--space-2: 0.5rem;     /* 8px */
--space-3: 0.75rem;    /* 12px */
--space-4: 1rem;       /* 16px */
--space-5: 1.25rem;    /* 20px */
--space-6: 1.5rem;     /* 24px */
--space-8: 2rem;       /* 32px */
--space-10: 2.5rem;    /* 40px */
--space-12: 3rem;      /* 48px */
--space-16: 4rem;      /* 64px */
```

### Usage
```html
<div class="p-6">Padding 24px</div>
<div class="m-4">Margin 16px</div>
<div class="mt-8 mb-4">Top margin 32px, bottom margin 16px</div>
```

## 🔲 Border Radius

```css
--radius-sm: 0.375rem;  /* 6px */
--radius-md: 0.5rem;    /* 8px */
--radius-lg: 0.75rem;   /* 12px */
--radius-xl: 1rem;      /* 16px */
--radius-2xl: 1.5rem;   /* 24px */
```

## 🌫️ Shadows

```css
--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
--shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
```

## 🧩 Components

### Cards
```html
<div class="card">
  <div class="card-header">
    <h3 class="text-lg font-semibold">Card Title</h3>
  </div>
  <div class="card-body">
    <p>Card content goes here.</p>
  </div>
  <div class="card-footer">
    <button class="btn btn-primary">Action</button>
  </div>
</div>
```

### Buttons
```html
<!-- Primary Button -->
<button class="btn btn-primary">Primary Action</button>

<!-- Secondary Button -->
<button class="btn btn-secondary">Secondary Action</button>

<!-- Large Button -->
<button class="btn btn-primary btn-lg">Large Button</button>

<!-- Small Button -->
<button class="btn btn-secondary btn-sm">Small Button</button>

<!-- Loading State -->
<button class="btn btn-primary loading">Loading...</button>
```

### Forms
```html
<div class="form-group">
  <label class="form-label" for="input-id">Label</label>
  <input type="text" id="input-id" class="form-input" placeholder="Placeholder">
  <div class="form-error">Error message</div>
</div>
```

### Navigation
```html
<nav class="navbar">
  <div class="container flex justify-between items-center">
    <a href="#" class="navbar-brand">WASH MIS</a>
    <div class="navbar-nav">
      <a href="#" class="nav-link">Dashboard</a>
      <a href="#" class="nav-link">Projects</a>
      <a href="#" class="nav-link">Reports</a>
    </div>
  </div>
</nav>
```

## 📱 Responsive Design

### Breakpoints
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

### Grid System
```html
<!-- Responsive Grid -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  <div class="card">Item 1</div>
  <div class="card">Item 2</div>
  <div class="card">Item 3</div>
</div>
```

## ♿ Accessibility Guidelines

### Focus Management
- Visible focus indicators
- Logical tab order
- Skip links for navigation

### Color Contrast
- Minimum 4.5:1 for normal text
- Minimum 3:1 for large text
- Color is not the only indicator

### Screen Readers
- Semantic HTML elements
- ARIA labels and descriptions
- Alternative text for images

### Keyboard Navigation
- All interactive elements accessible via keyboard
- Escape key closes modals/dropdowns
- Enter/Space activates buttons

## 🎭 Animation & Transitions

### Timing Functions
```css
--transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
--transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
--transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
```

### Reduced Motion
```css
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
```

## 🚀 Usage

### Installation
1. Include the CSS file in your HTML:
```html
<link rel="stylesheet" href="/styles/wash-mis-design-system.css">
```

2. Use the component classes:
```html
<div class="container">
  <div class="card">
    <div class="card-body">
      <h2 class="text-xl font-semibold mb-4">Welcome to WASH MIS</h2>
      <p class="text-gray-600 mb-6">Manage water, sanitation, and hygiene infrastructure.</p>
      <button class="btn btn-primary">Get Started</button>
    </div>
  </div>
</div>
```

## 🔧 Customization

### CSS Custom Properties
Override design tokens by redefining CSS custom properties:

```css
:root {
  --primary-600: #your-brand-color;
  --font-family-sans: 'Your-Font', sans-serif;
}
```

### Component Variants
Extend existing components with additional classes:

```css
.btn-outline {
  background: transparent;
  border: 2px solid var(--primary-600);
  color: var(--primary-600);
}
```

## 📋 Best Practices

1. **Use semantic HTML** elements for better accessibility
2. **Follow the spacing scale** for consistent layouts
3. **Test with keyboard navigation** and screen readers
4. **Optimize for performance** by using efficient selectors
5. **Maintain consistency** across all interfaces
6. **Document custom components** for team collaboration

---

**Ministry of Infrastructure, Republic of Rwanda**  
*Building sustainable digital infrastructure for WASH management*
