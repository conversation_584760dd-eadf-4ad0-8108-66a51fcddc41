# Authentication System Documentation

## Overview

This NestJS application implements a comprehensive authentication system with the following features:

- JWT-based authentication with refresh tokens
- Two-Factor Authentication (2FA) using TOTP
- Role-based access control with privileges
- Location-based access control
- Mobile user restrictions
- Email verification and password reset
- Secure user management

## Architecture

### Core Components

1. **Auth Module** (`src/auth/`)
   - Authentication service and controller
   - JWT token management
   - 2FA implementation
   - Password reset functionality

2. **Users Module** (`src/users/`)
   - User management service and controller
   - User creation with privilege checks
   - Location access management

3. **Guards** (`src/common/guards/`)
   - `AuthGuard`: JWT token validation
   - `PrivilegeGuard`: Role-based access control
   - `MobileGuard`: Restricts mobile-only users from web endpoints

4. **Email System** (`src/email/`)
   - Handlebars templates with blue/white theme
   - User creation, password reset, 2FA setup emails

## Database Schema

The system uses the existing Prisma schema with these key models:

- `User`: Basic user information and role assignment
- `Account`: Authentication data (passwords, tokens, 2FA settings)
- `Role`: User roles with associated privileges
- `RecoveryCode`: 2FA backup codes
- `UserLocationAccess`: Geographic access restrictions

## API Endpoints

### Authentication Endpoints

#### POST `/api/v1/auth/login`
Standard login with email and password.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Response (No 2FA):**
```json
{
  "accessToken": "jwt_token",
  "refreshToken": "refresh_token",
  "user": {
    "id": "user_id",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "role": {
      "name": "Admin",
      "privileges": ["USER_MANAGEMENT"]
    }
  }
}
```

**Response (2FA Required):**
```json
{
  "requires2FA": true,
  "tempToken": "temp_token",
  "message": "Please enter your 2FA code"
}
```

#### POST `/api/v1/auth/verify-2fa`
Verify 2FA code during login.

**Request:**
```json
{
  "tempToken": "temp_token",
  "totpCode": "123456"
}
```

#### POST `/api/v1/auth/use-recovery-code`
Use recovery code when 2FA device is unavailable.

**Request:**
```json
{
  "tempToken": "temp_token",
  "recoveryCode": "ABCD-1234-EFGH-5678"
}
```

#### POST `/api/v1/auth/refresh`
Refresh access token.

**Request:**
```json
{
  "refreshToken": "refresh_token"
}
```

#### POST `/api/v1/auth/logout`
Logout and invalidate tokens.

**Headers:**
```
Authorization: Bearer <access_token>
```

### Password Management

#### POST `/api/v1/auth/request-password-reset`
Request password reset email.

**Request:**
```json
{
  "email": "<EMAIL>"
}
```

#### POST `/api/v1/auth/reset-password`
Reset password with token.

**Request:**
```json
{
  "token": "reset_token",
  "newPassword": "NewSecurePassword123!"
}
```

#### POST `/api/v1/auth/set-initial-password`
Set initial password for new users.

**Request:**
```json
{
  "token": "verification_token",
  "password": "SecurePassword123!"
}
```

### Two-Factor Authentication

#### GET `/api/v1/auth/2fa/setup`
Get QR code and secret for 2FA setup.

**Headers:**
```
Authorization: Bearer <access_token>
```

**Response:**
```json
{
  "secret": "JBSWY3DPEHPK3PXP",
  "qrCode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "manualEntryKey": "otpauth://totp/..."
}
```

#### POST `/api/v1/auth/2fa/enable`
Enable 2FA with verification code.

**Request:**
```json
{
  "totpCode": "123456"
}
```

**Response:**
```json
{
  "message": "2FA has been enabled successfully",
  "recoveryCodes": [
    "ABCD-1234-EFGH-5678",
    "IJKL-9012-MNOP-3456"
  ]
}
```

#### POST `/api/v1/auth/2fa/disable`
Disable 2FA.

**Request:**
```json
{
  "totpCode": "123456"
}
```

#### POST `/api/v1/auth/2fa/regenerate-recovery-codes`
Generate new recovery codes.

**Request:**
```json
{
  "totpCode": "123456"
}
```

### User Management

#### POST `/api/v1/users`
Create new user (requires USER_MANAGEMENT privilege).

**Request:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "telephoneNumber": "+250788123456",
  "roleId": "role_id",
  "locations": [
    {
      "provinceId": 1,
      "districtId": 101
    }
  ]
}
```

#### GET `/api/v1/users`
Get all users with pagination and filtering.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `search`: Search by name or email
- `roleId`: Filter by role ID

#### GET `/api/v1/users/me`
Get current user profile.

#### GET `/api/v1/users/:id`
Get user by ID.

#### PATCH `/api/v1/users/:id`
Update user.

#### DELETE `/api/v1/users/:id`
Delete user.

#### POST `/api/v1/users/:id/resend-verification`
Resend verification email.

## Security Features

### Guards and Access Control

1. **AuthGuard**: Validates JWT tokens and attaches user to request
2. **PrivilegeGuard**: Checks user privileges using `@Privileges()` decorator
3. **MobileGuard**: Prevents mobile-only users from accessing web endpoints

### Usage Example:
```typescript
@Controller('admin')
@UseGuards(AuthGuard, MobileGuard, PrivilegeGuard)
@Privileges(Privilege.USER_MANAGEMENT)
export class AdminController {
  // Only authenticated users with USER_MANAGEMENT privilege
  // and not mobile-only users can access these endpoints
}
```

### Password Requirements

- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character (@$!%*?&)

### Token Security

- Access tokens expire in 15 minutes (configurable)
- Refresh tokens expire in 7 days (configurable)
- Tokens are invalidated on logout
- 2FA temporary tokens expire in 10 minutes

## Email Templates

The system includes responsive email templates with a blue and white theme:

1. **User Creation**: Welcome email with account setup instructions
2. **Password Reset**: Secure password reset with expiring links
3. **2FA Setup**: Instructions for setting up two-factor authentication
4. **Account Verification**: Email address verification

## Environment Configuration

Required environment variables:

```env
# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-here"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_SECRET="your-super-secret-refresh-key-here"
JWT_REFRESH_EXPIRES_IN="7d"

# Application
APP_NAME="MoU Management System"
FRONTEND_URL="http://localhost:3000"

# Email Support
SUPPORT_EMAIL="<EMAIL>"
SUPPORT_PHONE="+250 788 123 456"
```

## User Creation Flow

1. Admin creates user with USER_MANAGEMENT privilege
2. System generates verification token (24h expiry)
3. Welcome email sent with setup instructions
4. User clicks link to set initial password
5. Account verified and password set
6. 2FA setup email sent
7. User sets up 2FA for enhanced security

## Mobile vs Web Access

The system distinguishes between mobile data collection users and web application users:

- Users with only DATA_COLLECTION privilege are mobile-only
- Mobile-only users cannot access web application endpoints
- MobileGuard enforces this restriction

## Error Handling

The system provides comprehensive error handling with appropriate HTTP status codes:

- 400: Bad Request (validation errors, invalid data)
- 401: Unauthorized (invalid credentials, expired tokens)
- 403: Forbidden (insufficient privileges, mobile restrictions)
- 404: Not Found (user/resource not found)
- 409: Conflict (duplicate email/phone)

## Testing

To test the authentication system:

1. Set up environment variables
2. Run database migrations
3. Seed initial roles and admin user
4. Use API documentation at `/api-docs`
5. Test email functionality with MailHog (localhost:1025)

## Security Best Practices

1. Use strong JWT secrets in production
2. Enable HTTPS in production
3. Configure proper CORS settings
4. Use real SMTP server for production emails
5. Regularly rotate JWT secrets
6. Monitor failed login attempts
7. Implement rate limiting for auth endpoints
