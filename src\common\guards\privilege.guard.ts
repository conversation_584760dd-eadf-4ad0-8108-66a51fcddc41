import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Privilege } from '@prisma/client';

@Injectable()
export class PrivilegeGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredPrivileges = this.reflector.getAllAndOverride<Privilege[]>(
      'privileges',
      [context.getHandler(), context.getClass()],
    );

    if (!requiredPrivileges) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user || !user.role || !user.role.privileges) {
      throw new ForbiddenException('Access denied: No privileges found');
    }

    const userPrivileges = user.role.privileges;
    const hasPrivilege = requiredPrivileges.some((privilege) =>
      userPrivileges.includes(privilege),
    );

    if (!hasPrivilege) {
      throw new ForbiddenException(
        `Access denied: Required privileges: ${requiredPrivileges.join(', ')}`,
      );
    }

    return true;
  }
}
