import { MailerService } from "@nestjs-modules/mailer";
import { Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  constructor(
    private readonly mailerService: MailerService,
    private configService: ConfigService
  ) { }

  private getBaseContext() {
    return {
      year: new Date().getFullYear(),
      logoUrl: this.configService.get('LOGO_URL'),
      supportEmail: this.configService.get('SUPPORT_EMAIL'),
      supportPhone: this.configService.get('SUPPORT_PHONE'),
      frontendUrl: this.configService.get('SUPPORT_PHONE'),
      socialLinks: {
        twitter: this.configService.get('TWITTER_URL'),
        facebook: this.configService.get('FACEBOOK_URL'),
        linkedin: this.configService.get('LINKEDIN_URL')
      },
      unsubscribeUrl: `${this.configService.get('FRONTEND_URL')}`,
      preferencesUrl: `${this.configService.get('FRONTEND_URL')}`
    };
  }
}