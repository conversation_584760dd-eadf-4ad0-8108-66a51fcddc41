import { MailerService } from "@nestjs-modules/mailer";
import { Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  constructor(
    private readonly mailerService: MailerService,
    private configService: ConfigService
  ) { }

  private getBaseContext() {
    return {
      year: new Date().getFullYear(),
      logoUrl: this.configService.get('LOGO_URL'),
      supportEmail: this.configService.get('SUPPORT_EMAIL'),
      supportPhone: this.configService.get('SUPPORT_PHONE'),
      frontendUrl: this.configService.get('SUPPORT_PHONE'),
      socialLinks: {
        twitter: this.configService.get('TWITTER_URL'),
        facebook: this.configService.get('FACEBOOK_URL'),
        linkedin: this.configService.get('LINKEDIN_URL')
      },
      unsubscribeUrl: `${this.configService.get('FRONTEND_URL')}`,
      preferencesUrl: `${this.configService.get('FRONTEND_URL')}`
    };
  }

  async sendUserCreationEmail(email: string, firstName: string, verificationToken: string): Promise<void> {
    try {
      const verificationUrl = `${this.configService.get('FRONTEND_URL')}/auth/set-password?token=${verificationToken}`;

      await this.mailerService.sendMail({
        to: email,
        subject: 'Welcome! Set up your account',
        template: 'user-creation',
        context: {
          ...this.getBaseContext(),
          firstName,
          verificationUrl,
          verificationToken,
        },
      });

      this.logger.log(`User creation email sent to ${email}`);
    } catch (error) {
      this.logger.error(`Failed to send user creation email to ${email}:`, error);
      throw error;
    }
  }

  async sendPasswordResetEmail(email: string, firstName: string, resetToken: string): Promise<void> {
    try {
      const resetUrl = `${this.configService.get('FRONTEND_URL')}/auth/reset-password?token=${resetToken}`;

      await this.mailerService.sendMail({
        to: email,
        subject: 'Password Reset Request',
        template: 'password-reset',
        context: {
          ...this.getBaseContext(),
          firstName,
          resetUrl,
          resetToken,
        },
      });

      this.logger.log(`Password reset email sent to ${email}`);
    } catch (error) {
      this.logger.error(`Failed to send password reset email to ${email}:`, error);
      throw error;
    }
  }

  async send2FASetupEmail(email: string, firstName: string): Promise<void> {
    try {
      const setupUrl = `${this.configService.get('FRONTEND_URL')}/auth/2fa-setup`;

      await this.mailerService.sendMail({
        to: email,
        subject: 'Set up Two-Factor Authentication',
        template: '2fa-setup',
        context: {
          ...this.getBaseContext(),
          firstName,
          setupUrl,
        },
      });

      this.logger.log(`2FA setup email sent to ${email}`);
    } catch (error) {
      this.logger.error(`Failed to send 2FA setup email to ${email}:`, error);
      throw error;
    }
  }

  async sendAccountVerificationEmail(email: string, firstName: string, verificationToken: string): Promise<void> {
    try {
      const verificationUrl = `${this.configService.get('FRONTEND_URL')}/auth/verify-account?token=${verificationToken}`;

      await this.mailerService.sendMail({
        to: email,
        subject: 'Verify your account',
        template: 'account-verification',
        context: {
          ...this.getBaseContext(),
          firstName,
          verificationUrl,
          verificationToken,
        },
      });

      this.logger.log(`Account verification email sent to ${email}`);
    } catch (error) {
      this.logger.error(`Failed to send account verification email to ${email}:`, error);
      throw error;
    }
  }
}