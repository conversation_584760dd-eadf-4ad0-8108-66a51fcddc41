import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Privilege } from '@prisma/client';

@Injectable()
export class MobileGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user || !user.role || !user.role.privileges) {
      throw new ForbiddenException('Access denied: No user information found');
    }

    const userPrivileges = user.role.privileges;
    
    // Check if user only has DATA_COLLECTION privilege (mobile-only user)
    const isMobileOnlyUser = 
      userPrivileges.includes(Privilege.DATA_COLLECTION) &&
      userPrivileges.length === 1;

    if (isMobileOnlyUser) {
      throw new ForbiddenException(
        'Access denied: Mobile-only users cannot access web application endpoints',
      );
    }

    return true;
  }
}
